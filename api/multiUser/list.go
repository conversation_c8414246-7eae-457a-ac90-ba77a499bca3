package multiUser

import (
	"base/core/xhttp"
	"base/service/multiUserService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page" validate:"min=1"`
		Limit int64 `json:"limit" validate:"min=1,max=100"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	// 构建查询条件
	filter := bson.M{}

	list, count, err := multiUserService.NewMultiUserService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resp := struct {
		List  interface{} `json:"list"`
		Count int64       `json:"count"`
		Page  int64       `json:"page"`
		Limit int64       `json:"limit"`
	}{
		List:  list,
		Count: count,
		Page:  req.Page,
		Limit: req.Limit,
	}

	xhttp.RespSuccess(ctx, resp)
}
