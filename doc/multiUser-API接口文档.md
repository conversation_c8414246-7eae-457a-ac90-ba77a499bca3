# MultiUser API 接口文档

## 概述
MultiUser模块用于管理多用户相关的操作，包括创建、更新、删除和查询用户信息。

## 基础信息
- **基础URL**: `/api/multi/user`
- **请求方式**: POST
- **请求头**: 
  - `Content-Type: application/json`
  - `Authorization: Bearer {token}` (部分接口需要)
  - `X-Env: {环境标识}` (必需)

## 接口列表

### 1. 创建用户
**接口地址**: `/api/multi/user/create`  
**请求方式**: POST  
**接口描述**: 创建新的多用户记录

#### 请求参数
```json
{
  "user_id": "string",     // 用户ID，24位字符串，必需
  "object_id": "string",   // 对象ID，24位字符串，必需
  "note": "string"         // 备注信息，可选
}
```

#### 响应示例
```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

#### 错误码
- `400`: 参数错误
- `500`: 服务器内部错误

---

### 2. 更新用户
**接口地址**: `/api/multi/user/update`  
**请求方式**: POST  
**接口描述**: 更新已存在的多用户记录

#### 请求参数
```json
{
  "user_id": "string",     // 用户ID，24位字符串，必需
  "note": "string"         // 备注信息，可选
}
```

#### 响应示例
```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

#### 错误码
- `400`: 参数错误，用户不存在该类型角色
- `500`: 服务器内部错误

---

### 3. 删除用户
**接口地址**: `/api/multi/user/delete`  
**请求方式**: POST  
**接口描述**: 删除指定的多用户记录

#### 请求参数
```json
{
  "user_id": "string"      // 用户ID，24位字符串，必需
}
```

#### 响应示例
```json
{
  "code": 0,
  "msg": "success",
  "data": null
}
```

#### 错误码
- `400`: 参数错误，用户不存在该类型角色
- `500`: 服务器内部错误

---

### 4. 查询用户列表
**接口地址**: `/api/multi/user/list`  
**请求方式**: POST  
**接口描述**: 分页查询所有多用户信息

#### 请求参数
```json
{
  "page": 1,               // 页码，最小值1，默认1
  "limit": 10              // 每页数量，最小值1，最大值100，默认10
}
```

#### 响应示例
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": "string",              // 记录ID
        "user_id": "string",         // 用户ID
        "object_id": "string",       // 对象ID
        "object_type": 1,            // 对象类型
        "note": "string",            // 备注信息
        "created_at": 1640995200000, // 创建时间戳(毫秒)
        "updated_at": 1640995200000  // 更新时间戳(毫秒)
      }
    ],
    "count": 100,            // 总记录数
    "page": 1,               // 当前页码
    "limit": 10              // 每页数量
  }
}
```

#### 错误码
- `400`: 参数错误
- `500`: 服务器内部错误

## 数据模型

### MultiUser 对象
```json
{
  "id": "string",              // 记录唯一标识
  "user_id": "string",         // 用户ID
  "object_id": "string",       // 关联对象ID
  "object_type": "number",     // 对象类型
  "note": "string",            // 备注信息
  "created_at": "number",      // 创建时间戳(毫秒)
  "updated_at": "number"       // 更新时间戳(毫秒)
}
```

## 注意事项
1. 所有接口都需要在请求头中包含 `X-Env` 环境标识
2. 用户ID必须是24位的有效ObjectID格式
3. 创建用户时，如果用户已存在该类型角色，会返回错误
4. 更新和删除操作需要用户已存在对应记录
5. 分页查询支持的最大每页数量为100条
